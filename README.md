# Hover Cards · 优雅悬停动态卡片

一个可直接打开的静态 Demo，展示两种层级的“鼠标悬停时优雅展示动态内容”的卡片：
- 基础款：纯 CSS 悬停动画（缩放、阴影增强、封面渐暗、文字上浮）
- 进阶款：3D 倾斜、视差位移、磁性光斑（鼠标跟随），含无障碍与移动端降级

文件结构
- [index.html](index.html): Demo 页面，包含基础款与进阶款卡片
- [styles.css](styles.css): 主题变量、布局与动效样式（仅 transform/opacity）
- [app.js](app.js): 主题切换、进阶效果开关、鼠标跟随计算与触屏降级
- README.md: 使用说明与参数

快速使用
1. 直接双击 index.html 在浏览器中打开。
2. 顶部“切换主题”按钮可在浅色/深色之间切换（持久化到 localStorage）。
3. 勾选“进阶效果”开启 3D 倾斜 + 视差 + 光斑（桌面默认开启，触屏默认关闭）。
4. 将鼠标移到进阶卡片上体验磁性光斑与柔和 3D 回弹。

设计目标与动效原则
- 仅动画 transform/opacity，避免重排回流，保证性能稳定。
- 悬停主要反馈：卡片 scale、阴影、封面饱和度/亮度变化；内容文字自下而上浮现。
- 进阶视觉：在细微范围内的 rotateX/rotateY（默认最大 8°）+ 封面反向视差；光泽层采用 radial-gradient 跟随鼠标。
- 可访问性：键盘 focus-visible 触发同等视觉反馈；prefers-reduced-motion 自动降级；触屏无 hover 时改为轻量点击展开语义。

浏览器与设备支持
- 现代浏览器（Chromium/Firefox/Safari 最新两个大版本）。
- 移动端：自动降级，不启用 3D/光斑，仅保留淡入与内容可读性。
- 深浅色：跟随系统（prefers-color-scheme）并支持手动切换。

可访问性与降级策略
- 键盘可达：卡片元素可聚焦（tabindex="0"），使用 :focus-visible 提供可见焦点与动效。
- Reduced Motion：检测 prefers-reduced-motion: reduce，禁用 3D/视差与放大，仅保留轻量过渡与可读性提升。
- 触屏行为：不依赖 :hover，点击卡片可切换“展开”状态（视觉上保持轻量，不做 3D 倾斜以防眩晕）。

主题与参数化
核心 CSS 自定义属性位于 :root，可按需调整：
- --card-scale: 悬停缩放强度（默认 1.03）
- --media-dim: 悬停时封面暗化强度（默认 0.15 / 深色提升到 0.25）
- --overlay-opacity-hover: 顶部叠层透明度
- --content-fade-duration: 文本浮现时长（默认 300ms）
- --easing: 缓动曲线（默认 cubic-bezier(.2,.8,.2,1)）
- --gloss-size / --gloss-alpha: 光斑大小与透明度
- --tilt-max: 最大倾斜角（默认 8deg）
- --parallax-shift: 视差位移像素（默认 12px）

进阶效果开关
- 顶部“进阶效果”复选框（id="enableAdvanced"）控制是否启用 3D/光斑。桌面端且未启用 Reduced Motion 时默认开启。
- 相关逻辑在 app.js 中以 pointer 事件 + requestAnimationFrame 实现节流。

代码要点
- 结构：.card 内含 .card__media（封面）、.card__overlay（渐变叠层）、.card__content（文本与按钮），进阶款额外 .card__gloss（光泽层）。
- CSS：使用 grid 布局组织内容，自下向上浮现；背景图通过 CSS 变量 --img 注入，代码简洁可复用。
- JS：进阶卡片通过 data-advanced + .card--advanced 标识；监听 pointerenter/leave/move 计算卡片局部坐标，并将其映射到 3D 倾斜与光斑中心。

常见定制
1) 修改卡片尺寸与比例
- 在 .card 上设置 min-block-size 或添加专用尺寸修饰类（如 .card--tall）。
2) 更换色彩体系
- 覆盖 :root 的主题色变量（--primary 等），或在 html[data-theme="dark"] 上重定义。
3) 增减内容槽位
- 在 .card__content 中按需添加统计、标签云、进度条等模块，并为其添加相同的浮现过渡（opacity + translateY）。

性能建议
- 批量卡片时，避免超高分辨率大图；使用 cover + 适当的容器尺寸。
